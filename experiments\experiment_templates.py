#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验模板
预定义的四种典型电机学实验
"""

from typing import Dict, Any, List


class ExperimentTemplates:
    """实验模板管理器"""

    def __init__(self):
        self.templates = {
            'dc_motor': self.get_dc_motor_template(),
            'induction_motor': self.get_induction_motor_template(),
            'sync_generator': self.get_sync_generator_template(),
            'transformer': self.get_transformer_template()
        }

    def get_template(self, experiment_type: str) -> Dict[str, Any]:
        """获取实验模板"""
        return self.templates.get(experiment_type, {})

    def get_dc_motor_template(self) -> Dict[str, Any]:
        """直流电机特性实验模板"""
        return {
            'name': '直流电机特性实验',
            'description': '研究直流电机的启动特性、调速特性和机械特性',
            'objectives': [
                '了解直流电机的机械特性',
                '掌握直流电机的启动过程',
                '学习电枢电阻对转速的影响',
                '理解电枢控制方式'
            ],
            'components': [
                {
                    'type': 'power_source',
                    'name': '直流电源',
                    'position': (100, 200),
                    'parameters': {
                        'voltage': 220.0,
                        'internal_resistance': 0.1
                    }
                },
                {
                    'type': 'dc_motor',
                    'name': '直流电机',
                    'position': (400, 200),
                    'parameters': {
                        'rated_voltage': 220.0,
                        'rated_power': 1000.0,
                        'resistance': 2.0,
                        'inductance': 0.1,
                        'back_emf_constant': 0.1
                    }
                },
                {
                    'type': 'resistor',
                    'name': '启动电阻',
                    'position': (250, 150),
                    'parameters': {
                        'resistance': 5.0
                    }
                },
                {
                    'type': 'ammeter',
                    'name': '电流表',
                    'position': (300, 200),
                    'parameters': {
                        'range': 10.0
                    }
                },
                {
                    'type': 'voltmeter',
                    'name': '电压表',
                    'position': (350, 250),
                    'parameters': {
                        'range': 300.0
                    }
                },
                {
                    'type': 'tachometer',
                    'name': '转速表',
                    'position': (450, 150),
                    'parameters': {
                        'range': 3000.0
                    }
                }
            ],
            'connections': [
                {
                    'from': {'component': '直流电源', 'port': 0},
                    'to': {'component': '启动电阻', 'port': 0}
                },
                {
                    'from': {'component': '启动电阻', 'port': 1},
                    'to': {'component': '电流表', 'port': 0}
                },
                {
                    'from': {'component': '电流表', 'port': 1},
                    'to': {'component': '直流电机', 'port': 0}
                },
                {
                    'from': {'component': '直流电机', 'port': 1},
                    'to': {'component': '直流电源', 'port': 1}
                }
            ],
            'measurements': [
                '电枢电流',
                '电枢电压',
                '转速',
                '转矩'
            ],
            'parameters': {
                'simulation_time': 3.0,
                'time_step': 0.001
            }
        }

    def get_induction_motor_template(self) -> Dict[str, Any]:
        """三相异步电机实验模板"""
        return {
            'name': '三相异步电机启动与运行特性实验',
            'description': '研究三相异步电机的启动特性、调速方法和机械特性',
            'objectives': [
                '掌握异步电机Y-Δ启动',
                '了解直接启动和转速特性',
                '观察电流变化和转速连接',
                '测量同步转速和转差率'
            ],
            'components': [
                {
                    'type': 'three_phase_source',
                    'name': '三相电源',
                    'position': (100, 200),
                    'parameters': {
                        'voltage': 380.0,
                        'frequency': 50.0
                    }
                },
                {
                    'type': 'induction_motor',
                    'name': '异步电机',
                    'position': (400, 200),
                    'parameters': {
                        'rated_voltage': 380.0,
                        'rated_power': 1500.0,
                        'poles': 4,
                        'slip': 0.05,
                        'stator_resistance': 1.0,
                        'rotor_resistance': 0.8
                    }
                },
                {
                    'type': 'contactor',
                    'name': '接触器',
                    'position': (250, 150),
                    'parameters': {
                        'rated_current': 10.0
                    }
                },
                {
                    'type': 'ammeter',
                    'name': '电流表A',
                    'position': (300, 180),
                    'parameters': {
                        'range': 15.0
                    }
                },
                {
                    'type': 'ammeter',
                    'name': '电流表B',
                    'position': (300, 200),
                    'parameters': {
                        'range': 15.0
                    }
                },
                {
                    'type': 'ammeter',
                    'name': '电流表C',
                    'position': (300, 220),
                    'parameters': {
                        'range': 15.0
                    }
                },
                {
                    'type': 'tachometer',
                    'name': '转速表',
                    'position': (450, 150),
                    'parameters': {
                        'range': 1500.0
                    }
                }
            ],
            'connections': [
                {
                    'from': {'component': '三相电源', 'port': 0},
                    'to': {'component': '接触器', 'port': 0}
                },
                {
                    'from': {'component': '接触器', 'port': 1},
                    'to': {'component': '电流表A', 'port': 0}
                },
                {
                    'from': {'component': '电流表A', 'port': 1},
                    'to': {'component': '异步电机', 'port': 0}
                }
            ],
            'measurements': [
                '三相电流',
                '线电压',
                '转速',
                '转差率',
                '功率因数'
            ],
            'parameters': {
                'simulation_time': 5.0,
                'time_step': 0.01
            }
        }

    def get_sync_generator_template(self) -> Dict[str, Any]:
        """同步发电机实验模板"""
        return {
            'name': '同步发电机并网运行实验',
            'description': '研究同步发电机的并网条件与功率调节',
            'objectives': [
                '观察同步发电机并网时的电压相位',
                '掌握功率因数可调功率因数',
                '了解并网条件',
                '学习有功无功功率控制'
            ],
            'components': [
                {
                    'type': 'sync_generator',
                    'name': '同步发电机',
                    'position': (300, 200),
                    'parameters': {
                        'rated_voltage': 400.0,
                        'rated_power': 2000.0,
                        'sync_reactance': 10.0,
                        'emf': 400.0
                    }
                },
                {
                    'type': 'three_phase_source',
                    'name': '电网',
                    'position': (100, 200),
                    'parameters': {
                        'voltage': 380.0,
                        'frequency': 50.0
                    }
                },
                {
                    'type': 'switch',
                    'name': '并网开关',
                    'position': (200, 200),
                    'parameters': {
                        'state': 'open'
                    }
                },
                {
                    'type': 'voltmeter',
                    'name': '电压表',
                    'position': (250, 150),
                    'parameters': {
                        'range': 500.0
                    }
                },
                {
                    'type': 'wattmeter',
                    'name': '功率表',
                    'position': (350, 150),
                    'parameters': {
                        'range': 3000.0
                    }
                },
                {
                    'type': 'sync_meter',
                    'name': '同步表',
                    'position': (200, 150),
                    'parameters': {
                        'type': 'phase_indicator'
                    }
                }
            ],
            'connections': [
                {
                    'from': {'component': '电网', 'port': 0},
                    'to': {'component': '并网开关', 'port': 0}
                },
                {
                    'from': {'component': '并网开关', 'port': 1},
                    'to': {'component': '同步发电机', 'port': 0}
                }
            ],
            'measurements': [
                '发电机电压',
                '电网电压',
                '相位差',
                '有功功率',
                '无功功率',
                '功率因数'
            ],
            'parameters': {
                'simulation_time': 2.0,
                'time_step': 0.001
            }
        }

    def get_transformer_template(self) -> Dict[str, Any]:
        """变压器特性实验模板"""
        return {
            'name': '变压器负载特性与效率实验',
            'description': '研究变压器的负载特性、短路试验和效率规律',
            'objectives': [
                '掌握空载试验、短路试验',
                '了解负载试验、负载特性',
                '测量变压器效率化规律',
                '理解电压调整率'
            ],
            'components': [
                {
                    'type': 'ac_source',
                    'name': '交流电源',
                    'position': (100, 200),
                    'parameters': {
                        'voltage': 220.0,
                        'frequency': 50.0,
                        'phase': 0.0
                    }
                },
                {
                    'type': 'transformer',
                    'name': '变压器',
                    'position': (300, 200),
                    'parameters': {
                        'primary_voltage': 220.0,
                        'secondary_voltage': 24.0,
                        'turns_ratio': 9.17,
                        'primary_resistance': 0.5,
                        'secondary_resistance': 0.05,
                        'leakage_reactance': 2.0
                    }
                },
                {
                    'type': 'resistor',
                    'name': '负载电阻',
                    'position': (450, 200),
                    'parameters': {
                        'resistance': 10.0
                    }
                },
                {
                    'type': 'voltmeter',
                    'name': '一次电压表',
                    'position': (200, 150),
                    'parameters': {
                        'range': 300.0
                    }
                },
                {
                    'type': 'voltmeter',
                    'name': '二次电压表',
                    'position': (400, 150),
                    'parameters': {
                        'range': 50.0
                    }
                },
                {
                    'type': 'ammeter',
                    'name': '一次电流表',
                    'position': (200, 200),
                    'parameters': {
                        'range': 5.0
                    }
                },
                {
                    'type': 'ammeter',
                    'name': '二次电流表',
                    'position': (400, 200),
                    'parameters': {
                        'range': 10.0
                    }
                },
                {
                    'type': 'wattmeter',
                    'name': '功率表',
                    'position': (250, 250),
                    'parameters': {
                        'range': 500.0
                    }
                }
            ],
            'connections': [
                {
                    'from': {'component': '交流电源', 'port': 0},
                    'to': {'component': '一次电流表', 'port': 0}
                },
                {
                    'from': {'component': '一次电流表', 'port': 1},
                    'to': {'component': '变压器', 'port': 0}
                },
                {
                    'from': {'component': '变压器', 'port': 1},
                    'to': {'component': '二次电流表', 'port': 0}
                },
                {
                    'from': {'component': '二次电流表', 'port': 1},
                    'to': {'component': '负载电阻', 'port': 0}
                }
            ],
            'measurements': [
                '一次电压',
                '二次电压',
                '一次电流',
                '二次电流',
                '输入功率',
                '输出功率',
                '效率',
                '电压调整率'
            ],
            'parameters': {
                'simulation_time': 1.0,
                'time_step': 0.0001
            }
        }

    def get_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取所有实验模板"""
        return self.templates

    def get_template_names(self) -> List[str]:
        """获取所有模板名称"""
        return list(self.templates.keys())
