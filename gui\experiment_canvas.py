#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验画布模块
支持元件拖拽、连线和电路编辑
"""

from PyQt5.QtWidgets import (QGraphicsView, QGraphicsScene, QGraphicsItem, 
                            QGraphicsPixmapItem, QGraphicsLineItem, QMenu)
from PyQt5.QtCore import Qt, QPointF, pyqtSignal, QRectF
from PyQt5.QtGui import QPen, QBrush, QColor, QPainter, QPixmap, QFont
import math


class ComponentItem(QGraphicsItem):
    """电路元件图形项"""
    
    def __init__(self, component_type, name, x=0, y=0):
        super().__init__()
        self.component_type = component_type
        self.name = name
        self.parameters = {}
        self.connections = []  # 连接点列表
        self.input_ports = []  # 输入端口
        self.output_ports = []  # 输出端口
        
        # 设置属性
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.ItemSendsGeometryChanges, True)
        
        # 设置位置
        self.setPos(x, y)
        
        # 初始化端口
        self.init_ports()
        
    def init_ports(self):
        """初始化端口位置"""
        if self.component_type == "power_source":
            self.output_ports = [QPointF(40, 20)]  # 右侧输出
            
        elif self.component_type == "resistor":
            self.input_ports = [QPointF(0, 20)]    # 左侧输入
            self.output_ports = [QPointF(40, 20)]  # 右侧输出
            
        elif self.component_type == "dc_motor":
            self.input_ports = [QPointF(0, 10), QPointF(0, 30)]  # 左侧两个输入
            
        elif self.component_type == "induction_motor":
            self.input_ports = [QPointF(0, 10), QPointF(0, 20), QPointF(0, 30)]  # 三相输入
            
        elif self.component_type == "ammeter":
            self.input_ports = [QPointF(0, 20)]
            self.output_ports = [QPointF(40, 20)]
            
        elif self.component_type == "voltmeter":
            self.input_ports = [QPointF(0, 10), QPointF(0, 30)]  # 并联测量
            
    def boundingRect(self):
        """返回边界矩形"""
        return QRectF(0, 0, 80, 40)
        
    def paint(self, painter, option, widget):
        """绘制元件"""
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置画笔和画刷
        pen = QPen(QColor(0, 0, 0), 2)
        if self.isSelected():
            pen.setColor(QColor(255, 0, 0))
        painter.setPen(pen)
        
        # 根据元件类型绘制不同形状
        if self.component_type == "power_source":
            self.draw_power_source(painter)
        elif self.component_type == "resistor":
            self.draw_resistor(painter)
        elif self.component_type == "dc_motor":
            self.draw_dc_motor(painter)
        elif self.component_type == "induction_motor":
            self.draw_induction_motor(painter)
        elif self.component_type == "ammeter":
            self.draw_ammeter(painter)
        elif self.component_type == "voltmeter":
            self.draw_voltmeter(painter)
        elif self.component_type == "transformer":
            self.draw_transformer(painter)
        else:
            # 默认矩形
            painter.drawRect(self.boundingRect())
            
        # 绘制元件名称
        painter.setFont(QFont("Arial", 8))
        painter.drawText(5, 35, self.name)
        
        # 绘制端口
        self.draw_ports(painter)
        
    def draw_power_source(self, painter):
        """绘制电源"""
        painter.drawEllipse(10, 10, 20, 20)
        painter.drawText(18, 22, "V")
        
    def draw_resistor(self, painter):
        """绘制电阻"""
        # 绘制锯齿形电阻符号
        points = [QPointF(10, 20), QPointF(15, 10), QPointF(25, 30), 
                 QPointF(35, 10), QPointF(45, 30), QPointF(50, 20)]
        for i in range(len(points) - 1):
            painter.drawLine(points[i], points[i + 1])
            
    def draw_dc_motor(self, painter):
        """绘制直流电机"""
        painter.drawEllipse(15, 5, 30, 30)
        painter.drawText(25, 22, "M")
        painter.drawText(30, 22, "~")
        
    def draw_induction_motor(self, painter):
        """绘制异步电机"""
        painter.drawEllipse(15, 5, 30, 30)
        painter.drawText(22, 22, "3~")
        
    def draw_ammeter(self, painter):
        """绘制电流表"""
        painter.drawEllipse(15, 10, 20, 20)
        painter.drawText(22, 22, "A")
        
    def draw_voltmeter(self, painter):
        """绘制电压表"""
        painter.drawEllipse(15, 10, 20, 20)
        painter.drawText(22, 22, "V")
        
    def draw_transformer(self, painter):
        """绘制变压器"""
        # 绘制两个线圈
        painter.drawEllipse(10, 10, 15, 20)
        painter.drawEllipse(35, 10, 15, 20)
        # 绘制铁芯
        painter.drawLine(27, 8, 27, 32)
        painter.drawLine(29, 8, 29, 32)
        
    def draw_ports(self, painter):
        """绘制连接端口"""
        port_pen = QPen(QColor(0, 255, 0), 3)
        painter.setPen(port_pen)
        
        # 绘制输入端口
        for port in self.input_ports:
            painter.drawEllipse(port.x() - 2, port.y() - 2, 4, 4)
            
        # 绘制输出端口
        for port in self.output_ports:
            painter.drawEllipse(port.x() - 2, port.y() - 2, 4, 4)
            
    def get_port_position(self, port_index, is_input=True):
        """获取端口的全局位置"""
        if is_input and port_index < len(self.input_ports):
            local_pos = self.input_ports[port_index]
        elif not is_input and port_index < len(self.output_ports):
            local_pos = self.output_ports[port_index]
        else:
            return None
            
        return self.mapToScene(local_pos)


class ConnectionLine(QGraphicsLineItem):
    """连接线"""
    
    def __init__(self, start_item, start_port, end_item=None, end_port=None):
        super().__init__()
        self.start_item = start_item
        self.start_port = start_port
        self.end_item = end_item
        self.end_port = end_port
        
        # 设置线条样式
        pen = QPen(QColor(0, 0, 255), 2)
        self.setPen(pen)
        
        self.update_line()
        
    def update_line(self):
        """更新连接线位置"""
        if self.start_item:
            start_pos = self.start_item.get_port_position(self.start_port, False)
            if start_pos:
                if self.end_item:
                    end_pos = self.end_item.get_port_position(self.end_port, True)
                    if end_pos:
                        self.setLine(start_pos.x(), start_pos.y(), 
                                   end_pos.x(), end_pos.y())


class ExperimentCanvas(QGraphicsView):
    """实验画布"""
    
    component_added = pyqtSignal(object)  # 元件添加信号
    component_selected = pyqtSignal(object)  # 元件选择信号
    
    def __init__(self):
        super().__init__()
        
        # 创建场景
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # 设置画布属性
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHint(QPainter.Antialiasing)
        
        # 当前选择的元件类型
        self.current_component_type = None
        
        # 连接模式
        self.connection_mode = False
        self.temp_line = None
        self.start_component = None
        self.start_port = None
        
        # 元件列表
        self.components = []
        self.connections = []
        
    def set_current_component(self, component_type):
        """设置当前选择的元件类型"""
        self.current_component_type = component_type
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            if self.current_component_type:
                # 添加新元件
                scene_pos = self.mapToScene(event.pos())
                self.add_component(self.current_component_type, scene_pos)
                self.current_component_type = None
            else:
                # 选择元件
                item = self.itemAt(event.pos())
                if isinstance(item, ComponentItem):
                    self.component_selected.emit(item)
                    
        super().mousePressEvent(event)
        
    def add_component(self, component_type, position):
        """添加元件到画布"""
        # 生成元件名称
        component_name = f"{component_type}_{len(self.components) + 1}"
        
        # 创建元件
        component = ComponentItem(component_type, component_name, 
                                position.x(), position.y())
        
        # 添加到场景
        self.scene.addItem(component)
        self.components.append(component)
        
        # 发送信号
        self.component_added.emit(component)
        
    def clear_canvas(self):
        """清空画布"""
        self.scene.clear()
        self.components.clear()
        self.connections.clear()
        
    def get_circuit_configuration(self):
        """获取电路配置"""
        config = {
            'components': [],
            'connections': []
        }
        
        # 添加元件信息
        for component in self.components:
            comp_info = {
                'type': component.component_type,
                'name': component.name,
                'position': (component.x(), component.y()),
                'parameters': component.parameters
            }
            config['components'].append(comp_info)
            
        # 添加连接信息
        for connection in self.connections:
            conn_info = {
                'start_component': connection.start_item.name,
                'start_port': connection.start_port,
                'end_component': connection.end_item.name,
                'end_port': connection.end_port
            }
            config['connections'].append(conn_info)
            
        return config
        
    def update_component_parameter(self, component_name, parameter_name, value):
        """更新元件参数"""
        for component in self.components:
            if component.name == component_name:
                component.parameters[parameter_name] = value
                break
