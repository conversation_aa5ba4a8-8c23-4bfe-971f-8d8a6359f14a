#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试仿真功能
验证电机学仿真软件的核心功能
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from experiments.experiment_manager import ExperimentManager


def test_dc_motor_simulation():
    """测试直流电机仿真"""
    print("测试直流电机仿真...")
    
    # 创建实验管理器
    manager = ExperimentManager()
    
    # 构建测试电路配置
    circuit_config = {
        'components': [
            {
                'type': 'power_source',
                'name': 'DC_Source',
                'parameters': {
                    'voltage': 220.0,
                    'internal_resistance': 0.1
                }
            },
            {
                'type': 'dc_motor',
                'name': 'DC_Motor',
                'parameters': {
                    'rated_voltage': 220.0,
                    'rated_power': 1000.0,
                    'resistance': 2.0,
                    'inductance': 0.1,
                    'back_emf_constant': 0.1
                }
            }
        ],
        'connections': []
    }
    
    try:
        # 运行仿真
        results = manager.run_simulation(circuit_config)
        
        # 检查结果
        if 'waveform' in results:
            waveform = results['waveform']
            print(f"✓ 波形数据: 时间点数 = {len(waveform['time'])}")
            print(f"✓ 最大电流: {max(waveform['current']):.3f} A")
            print(f"✓ 稳态电流: {waveform['current'][-1]:.3f} A")
            
        if 'characteristic' in results:
            char = results['characteristic']
            print(f"✓ 特性曲线: {len(char['x_data'])} 个数据点")
            
        if 'table_data' in results:
            table = results['table_data']
            print(f"✓ 数据表格: {len(table['data'])} 行数据")
            
        print("直流电机仿真测试通过！\n")
        return True
        
    except Exception as e:
        print(f"✗ 直流电机仿真测试失败: {e}\n")
        return False


def test_induction_motor_simulation():
    """测试异步电机仿真"""
    print("测试异步电机仿真...")
    
    manager = ExperimentManager()
    
    circuit_config = {
        'components': [
            {
                'type': 'three_phase_source',
                'name': 'Three_Phase_Source',
                'parameters': {
                    'voltage': 380.0,
                    'frequency': 50.0
                }
            },
            {
                'type': 'induction_motor',
                'name': 'Induction_Motor',
                'parameters': {
                    'rated_voltage': 380.0,
                    'rated_power': 1500.0,
                    'poles': 4,
                    'slip': 0.05
                }
            }
        ],
        'connections': []
    }
    
    try:
        results = manager.run_simulation(circuit_config)
        
        if 'waveform' in results:
            waveform = results['waveform']
            print(f"✓ 启动过程: 时间点数 = {len(waveform['time'])}")
            
        if 'characteristic' in results:
            char = results['characteristic']
            print(f"✓ 机械特性: 转速范围 {min(char['x_data']):.1f} - {max(char['x_data']):.1f} rpm")
            
        print("异步电机仿真测试通过！\n")
        return True
        
    except Exception as e:
        print(f"✗ 异步电机仿真测试失败: {e}\n")
        return False


def test_transformer_simulation():
    """测试变压器仿真"""
    print("测试变压器仿真...")
    
    manager = ExperimentManager()
    
    circuit_config = {
        'components': [
            {
                'type': 'ac_source',
                'name': 'AC_Source',
                'parameters': {
                    'voltage': 220.0,
                    'frequency': 50.0
                }
            },
            {
                'type': 'transformer',
                'name': 'Transformer',
                'parameters': {
                    'primary_voltage': 220.0,
                    'secondary_voltage': 24.0,
                    'turns_ratio': 9.17
                }
            }
        ],
        'connections': []
    }
    
    try:
        results = manager.run_simulation(circuit_config)
        
        if 'waveform' in results:
            waveform = results['waveform']
            print(f"✓ 电压波形: 峰值 = {max(waveform['voltage']):.1f} V")
            
        if 'characteristic' in results:
            char = results['characteristic']
            print(f"✓ 效率特性: 最大效率 = {max(char['y_data']):.1f}%")
            
        print("变压器仿真测试通过！\n")
        return True
        
    except Exception as e:
        print(f"✗ 变压器仿真测试失败: {e}\n")
        return False


def test_experiment_templates():
    """测试实验模板"""
    print("测试实验模板...")
    
    manager = ExperimentManager()
    
    # 测试加载模板
    templates = ['dc_motor', 'induction_motor', 'sync_generator', 'transformer']
    
    for template_name in templates:
        try:
            success = manager.load_experiment_template(template_name)
            if success:
                print(f"✓ {template_name} 模板加载成功")
            else:
                print(f"✗ {template_name} 模板加载失败")
        except Exception as e:
            print(f"✗ {template_name} 模板加载异常: {e}")
            
    print("实验模板测试完成！\n")


def plot_test_results():
    """绘制测试结果"""
    print("生成测试结果图表...")
    
    # 创建简单的测试数据
    t = np.linspace(0, 2, 1000)
    
    # 直流电机启动电流
    i_dc = 5 * (1 - np.exp(-t/0.5)) + 1
    
    # 异步电机转速
    n_async = 1450 * (1 - np.exp(-t/1.0))
    
    # 变压器效率
    load = np.linspace(0.1, 1.0, 50)
    efficiency = 100 * (1 - 0.05/load - 0.02*load)
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
    
    # 直流电机启动电流
    ax1.plot(t, i_dc, 'b-', linewidth=2)
    ax1.set_title('直流电机启动电流')
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('电流 (A)')
    ax1.grid(True, alpha=0.3)
    
    # 异步电机转速
    ax2.plot(t, n_async, 'r-', linewidth=2)
    ax2.set_title('异步电机启动转速')
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('转速 (rpm)')
    ax2.grid(True, alpha=0.3)
    
    # 变压器效率
    ax3.plot(load, efficiency, 'g-', linewidth=2)
    ax3.set_title('变压器效率特性')
    ax3.set_xlabel('负载率')
    ax3.set_ylabel('效率 (%)')
    ax3.grid(True, alpha=0.3)
    
    # 同步发电机功率角特性
    delta = np.linspace(0, 90, 100)
    P = np.sin(delta * np.pi / 180)
    ax4.plot(delta, P, 'm-', linewidth=2)
    ax4.set_title('同步发电机功率特性')
    ax4.set_xlabel('功率角 (度)')
    ax4.set_ylabel('标幺功率')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_results.png', dpi=150, bbox_inches='tight')
    print("✓ 测试结果图表已保存为 test_results.png")
    plt.show()


def main():
    """主测试函数"""
    print("=" * 50)
    print("电机学仿真软件功能测试")
    print("=" * 50)
    
    # 运行各项测试
    tests = [
        test_dc_motor_simulation,
        test_induction_motor_simulation, 
        test_transformer_simulation,
        test_experiment_templates
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
            
    # 生成测试图表
    try:
        plot_test_results()
    except Exception as e:
        print(f"图表生成失败: {e}")
        
    # 测试总结
    print("=" * 50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    if passed == total:
        print("✓ 所有测试通过！软件功能正常。")
    else:
        print(f"✗ {total - passed} 项测试失败，请检查相关功能。")
    print("=" * 50)


if __name__ == '__main__':
    main()
