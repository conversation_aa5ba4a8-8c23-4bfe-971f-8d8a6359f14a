#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数面板模块
显示和编辑元件参数
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QGroupBox, QScrollArea,
                            QDoubleSpinBox, QSpinBox, QComboBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QDoubleValidator, QIntValidator


class ParameterWidget(QWidget):
    """参数编辑控件"""
    
    value_changed = pyqtSignal(str, object)  # 参数名, 新值
    
    def __init__(self, param_name, param_value, param_type="float"):
        super().__init__()
        self.param_name = param_name
        self.param_type = param_type
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 参数名标签
        name_label = QLabel(param_name + ":")
        name_label.setFixedWidth(80)
        layout.addWidget(name_label)
        
        # 根据类型创建编辑控件
        if param_type == "float":
            self.editor = QDoubleSpinBox()
            self.editor.setRange(-999999.0, 999999.0)
            self.editor.setDecimals(3)
            self.editor.setValue(float(param_value))
            self.editor.valueChanged.connect(self.on_value_changed)
            
        elif param_type == "int":
            self.editor = QSpinBox()
            self.editor.setRange(-999999, 999999)
            self.editor.setValue(int(param_value))
            self.editor.valueChanged.connect(self.on_value_changed)
            
        elif param_type == "bool":
            self.editor = QCheckBox()
            self.editor.setChecked(bool(param_value))
            self.editor.stateChanged.connect(self.on_value_changed)
            
        elif param_type == "choice":
            self.editor = QComboBox()
            if isinstance(param_value, dict) and "options" in param_value:
                self.editor.addItems(param_value["options"])
                if "default" in param_value:
                    self.editor.setCurrentText(param_value["default"])
            self.editor.currentTextChanged.connect(self.on_value_changed)
            
        else:  # string
            self.editor = QLineEdit()
            self.editor.setText(str(param_value))
            self.editor.textChanged.connect(self.on_value_changed)
            
        layout.addWidget(self.editor)
        
    def on_value_changed(self, value):
        """值改变事件"""
        if self.param_type == "bool":
            value = self.editor.isChecked()
        elif self.param_type == "float":
            value = self.editor.value()
        elif self.param_type == "int":
            value = self.editor.value()
        else:
            value = str(value)
            
        self.value_changed.emit(self.param_name, value)
        
    def get_value(self):
        """获取当前值"""
        if self.param_type == "bool":
            return self.editor.isChecked()
        elif self.param_type == "float":
            return self.editor.value()
        elif self.param_type == "int":
            return self.editor.value()
        else:
            return self.editor.text()


class ParameterPanel(QWidget):
    """参数面板"""
    
    parameter_changed = pyqtSignal(str, str, object)  # 元件名, 参数名, 新值
    simulation_requested = pyqtSignal()  # 仿真请求信号
    
    def __init__(self):
        super().__init__()
        self.setFixedWidth(350)
        self.current_component = None
        self.parameter_widgets = {}
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("参数设置")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 元件信息组
        self.component_info_group = QGroupBox("元件信息")
        self.component_info_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(self.component_info_group)
        
        info_layout = QVBoxLayout(self.component_info_group)
        
        self.component_name_label = QLabel("未选择元件")
        self.component_name_label.setFont(QFont("Microsoft YaHei", 10))
        info_layout.addWidget(self.component_name_label)
        
        self.component_type_label = QLabel("")
        self.component_type_label.setFont(QFont("Microsoft YaHei", 9))
        self.component_type_label.setStyleSheet("color: #666666;")
        info_layout.addWidget(self.component_type_label)
        
        # 参数编辑组
        self.parameter_group = QGroupBox("参数编辑")
        self.parameter_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(self.parameter_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.parameter_content = QWidget()
        self.parameter_layout = QVBoxLayout(self.parameter_content)
        scroll_area.setWidget(self.parameter_content)
        
        param_layout = QVBoxLayout(self.parameter_group)
        param_layout.addWidget(scroll_area)
        
        # 仿真控制组
        simulation_group = QGroupBox("仿真控制")
        simulation_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        layout.addWidget(simulation_group)
        
        sim_layout = QVBoxLayout(simulation_group)
        
        # 仿真参数
        self.sim_time_widget = ParameterWidget("仿真时间(s)", 1.0, "float")
        sim_layout.addWidget(self.sim_time_widget)
        
        self.sim_step_widget = ParameterWidget("时间步长(s)", 0.001, "float")
        sim_layout.addWidget(self.sim_step_widget)
        
        # 仿真按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始仿真")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        self.start_button.clicked.connect(self.on_simulation_requested)
        button_layout.addWidget(self.start_button)
        
        self.reset_button = QPushButton("重置参数")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.reset_button.clicked.connect(self.reset_parameters)
        button_layout.addWidget(self.reset_button)
        
        sim_layout.addLayout(button_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
    def show_component_parameters(self, component):
        """显示元件参数"""
        self.current_component = component
        
        # 更新元件信息
        self.component_name_label.setText(f"名称: {component.name}")
        self.component_type_label.setText(f"类型: {component.component_type}")
        
        # 清除旧的参数控件
        self.clear_parameter_widgets()
        
        # 获取元件的默认参数
        default_params = self.get_default_parameters(component.component_type)
        
        # 合并已有参数和默认参数
        all_params = default_params.copy()
        all_params.update(component.parameters)
        
        # 创建参数编辑控件
        for param_name, param_info in all_params.items():
            if isinstance(param_info, dict):
                param_value = param_info.get("value", param_info.get("default", 0))
                param_type = param_info.get("type", "float")
            else:
                param_value = param_info
                param_type = "float"
                
            widget = ParameterWidget(param_name, param_value, param_type)
            widget.value_changed.connect(self.on_parameter_changed)
            
            self.parameter_widgets[param_name] = widget
            self.parameter_layout.addWidget(widget)
            
        # 添加弹性空间
        self.parameter_layout.addStretch()
        
    def clear_parameter_widgets(self):
        """清除参数控件"""
        for widget in self.parameter_widgets.values():
            widget.deleteLater()
        self.parameter_widgets.clear()
        
        # 清除布局中的所有控件
        while self.parameter_layout.count():
            child = self.parameter_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def on_parameter_changed(self, param_name, value):
        """参数改变事件"""
        if self.current_component:
            self.current_component.parameters[param_name] = value
            self.parameter_changed.emit(self.current_component.name, param_name, value)
            
    def on_simulation_requested(self):
        """仿真请求事件"""
        self.simulation_requested.emit()
        
    def reset_parameters(self):
        """重置参数"""
        if self.current_component:
            # 重置为默认参数
            default_params = self.get_default_parameters(self.current_component.component_type)
            self.current_component.parameters = {}
            
            # 重新显示参数
            self.show_component_parameters(self.current_component)
            
    def update_component_list(self, component):
        """更新元件列表"""
        # 这里可以添加元件列表管理功能
        pass
        
    def clear_parameters(self):
        """清空参数面板"""
        self.current_component = None
        self.component_name_label.setText("未选择元件")
        self.component_type_label.setText("")
        self.clear_parameter_widgets()
        
    def get_default_parameters(self, component_type):
        """获取元件的默认参数"""
        defaults = {
            "power_source": {
                "voltage": {"value": 12.0, "type": "float"},
                "internal_resistance": {"value": 0.1, "type": "float"}
            },
            "ac_source": {
                "voltage": {"value": 220.0, "type": "float"},
                "frequency": {"value": 50.0, "type": "float"},
                "phase": {"value": 0.0, "type": "float"}
            },
            "dc_motor": {
                "rated_voltage": {"value": 220.0, "type": "float"},
                "rated_power": {"value": 1000.0, "type": "float"},
                "resistance": {"value": 2.0, "type": "float"},
                "inductance": {"value": 0.1, "type": "float"},
                "back_emf_constant": {"value": 0.1, "type": "float"}
            },
            "induction_motor": {
                "rated_voltage": {"value": 380.0, "type": "float"},
                "rated_power": {"value": 1500.0, "type": "float"},
                "poles": {"value": 4, "type": "int"},
                "slip": {"value": 0.05, "type": "float"},
                "stator_resistance": {"value": 1.0, "type": "float"},
                "rotor_resistance": {"value": 0.8, "type": "float"}
            },
            "resistor": {
                "resistance": {"value": 10.0, "type": "float"}
            },
            "inductor": {
                "inductance": {"value": 0.1, "type": "float"}
            },
            "capacitor": {
                "capacitance": {"value": 100e-6, "type": "float"}
            },
            "transformer": {
                "primary_voltage": {"value": 220.0, "type": "float"},
                "secondary_voltage": {"value": 24.0, "type": "float"},
                "turns_ratio": {"value": 9.17, "type": "float"},
                "primary_resistance": {"value": 0.5, "type": "float"},
                "secondary_resistance": {"value": 0.05, "type": "float"}
            }
        }
        
        return defaults.get(component_type, {})
