#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验管理器
管理四种典型电机学实验
"""

import numpy as np
from typing import Dict, List, Any
from .simulation_engine import SimulationEngine
from .experiment_templates import ExperimentTemplates


class ExperimentManager:
    """实验管理器"""
    
    def __init__(self):
        self.simulation_engine = SimulationEngine()
        self.templates = ExperimentTemplates()
        self.current_experiment = None
        self.is_running = False
        
    def load_experiment_template(self, experiment_type: str):
        """加载实验模板"""
        template = self.templates.get_template(experiment_type)
        if template:
            self.current_experiment = {
                'type': experiment_type,
                'name': template['name'],
                'description': template['description'],
                'components': template['components'],
                'connections': template['connections'],
                'parameters': template.get('parameters', {})
            }
            return True
        return False
        
    def run_simulation(self, circuit_config: Dict) -> Dict:
        """运行仿真"""
        if not circuit_config or not circuit_config.get('components'):
            raise ValueError("电路配置为空或无效")
            
        self.is_running = True
        
        try:
            # 根据电路类型选择仿真方法
            experiment_type = self.detect_experiment_type(circuit_config)
            
            if experiment_type == 'dc_motor':
                results = self.simulate_dc_motor(circuit_config)
            elif experiment_type == 'induction_motor':
                results = self.simulate_induction_motor(circuit_config)
            elif experiment_type == 'sync_generator':
                results = self.simulate_sync_generator(circuit_config)
            elif experiment_type == 'transformer':
                results = self.simulate_transformer(circuit_config)
            else:
                results = self.simulate_basic_circuit(circuit_config)
                
            return results
            
        except Exception as e:
            raise RuntimeError(f"仿真失败: {str(e)}")
        finally:
            self.is_running = False
            
    def detect_experiment_type(self, circuit_config: Dict) -> str:
        """检测实验类型"""
        components = circuit_config.get('components', [])
        
        # 统计元件类型
        component_types = [comp['type'] for comp in components]
        
        if 'dc_motor' in component_types:
            return 'dc_motor'
        elif 'induction_motor' in component_types:
            return 'induction_motor'
        elif 'sync_generator' in component_types:
            return 'sync_generator'
        elif 'transformer' in component_types:
            return 'transformer'
        else:
            return 'basic_circuit'
            
    def simulate_dc_motor(self, circuit_config: Dict) -> Dict:
        """直流电机仿真"""
        # 获取电机参数
        motor_params = self.get_component_parameters(circuit_config, 'dc_motor')
        source_params = self.get_component_parameters(circuit_config, 'power_source')
        
        if not motor_params or not source_params:
            raise ValueError("缺少必要的元件参数")
            
        # 仿真参数
        t_end = 2.0  # 仿真时间
        dt = 0.001   # 时间步长
        time = np.arange(0, t_end, dt)
        
        # 电机参数
        V_source = source_params.get('voltage', 220.0)
        R = motor_params.get('resistance', 2.0)
        L = motor_params.get('inductance', 0.1)
        Ke = motor_params.get('back_emf_constant', 0.1)
        
        # 初始化变量
        current = np.zeros_like(time)
        voltage = np.zeros_like(time)
        speed = np.zeros_like(time)
        torque = np.zeros_like(time)
        
        # 数值积分求解
        i_prev = 0
        w_prev = 0
        
        for idx, t in enumerate(time):
            # 反电动势
            back_emf = Ke * w_prev
            
            # 电流微分方程: L*di/dt = V - R*i - Ke*w
            di_dt = (V_source - R * i_prev - back_emf) / L
            i_current = i_prev + di_dt * dt
            
            # 转速计算 (简化模型)
            T_motor = Ke * i_current  # 电磁转矩
            J = 0.01  # 转动惯量
            dw_dt = T_motor / J
            w_current = w_prev + dw_dt * dt
            
            # 存储结果
            current[idx] = i_current
            voltage[idx] = V_source
            speed[idx] = w_current * 60 / (2 * np.pi)  # 转换为rpm
            torque[idx] = T_motor
            
            # 更新前一步值
            i_prev = i_current
            w_prev = w_current
            
        # 生成特性曲线数据
        speed_points = np.linspace(0, max(speed), 10)
        torque_points = Ke * (V_source - Ke * speed_points * 2 * np.pi / 60) / R
        
        # 生成数据表格
        table_data = []
        for i in range(0, len(time), len(time)//20):  # 取20个数据点
            table_data.append([
                f"{time[i]:.3f}",
                f"{voltage[i]:.2f}",
                f"{current[i]:.3f}",
                f"{speed[i]:.1f}",
                f"{torque[i]:.3f}"
            ])
            
        return {
            'waveform': {
                'time': time,
                'voltage': voltage,
                'current': current,
                'title': '直流电机启动特性'
            },
            'characteristic': {
                'x_data': speed_points,
                'y_data': torque_points,
                'x_label': '转速 (rpm)',
                'y_label': '转矩 (N·m)',
                'title': '直流电机机械特性'
            },
            'table_data': {
                'headers': ['时间(s)', '电压(V)', '电流(A)', '转速(rpm)', '转矩(N·m)'],
                'data': table_data
            }
        }
        
    def simulate_induction_motor(self, circuit_config: Dict) -> Dict:
        """异步电机仿真"""
        motor_params = self.get_component_parameters(circuit_config, 'induction_motor')
        source_params = self.get_component_parameters(circuit_config, 'three_phase_source')
        
        if not motor_params or not source_params:
            raise ValueError("缺少必要的元件参数")
            
        # 电机参数
        V_rated = motor_params.get('rated_voltage', 380.0)
        P_rated = motor_params.get('rated_power', 1500.0)
        poles = motor_params.get('poles', 4)
        s_rated = motor_params.get('slip', 0.05)
        
        # 同步转速
        f = source_params.get('frequency', 50.0)
        n_sync = 120 * f / poles
        
        # 转差率范围
        slip = np.linspace(0.001, 1.0, 100)
        
        # 简化的异步电机模型
        R1 = 1.0  # 定子电阻
        R2 = 0.8  # 转子电阻
        X1 = 2.0  # 定子电抗
        X2 = 2.0  # 转子电抗
        
        # 计算转矩特性
        torque = []
        current = []
        
        for s in slip:
            # 转子阻抗
            Z2 = R2/s + 1j*X2
            # 总阻抗
            Z_total = R1 + 1j*X1 + Z2
            # 电流
            I = V_rated / abs(Z_total)
            current.append(I)
            
            # 转矩
            T = 3 * V_rated**2 * (R2/s) / (2*np.pi*f * abs(Z_total)**2)
            torque.append(T)
            
        # 转速
        speed = n_sync * (1 - slip)
        
        # 时间域仿真 (启动过程)
        t_end = 3.0
        dt = 0.01
        time = np.arange(0, t_end, dt)
        
        # 启动电流和转速
        start_current = np.zeros_like(time)
        start_speed = np.zeros_like(time)
        
        for i, t in enumerate(time):
            if t < 0.1:  # 启动瞬间
                start_current[i] = 6 * I  # 启动电流约为额定电流的6倍
                start_speed[i] = 0
            else:
                # 指数上升到稳态
                tau = 0.5  # 时间常数
                start_speed[i] = n_sync * (1 - s_rated) * (1 - np.exp(-(t-0.1)/tau))
                start_current[i] = I * (1 + 5*np.exp(-(t-0.1)/tau))
                
        return {
            'waveform': {
                'time': time,
                'voltage': np.full_like(time, V_rated),
                'current': start_current,
                'title': '异步电机启动特性'
            },
            'characteristic': {
                'x_data': speed,
                'y_data': torque,
                'x_label': '转速 (rpm)',
                'y_label': '转矩 (N·m)',
                'title': '异步电机机械特性'
            },
            'table_data': {
                'headers': ['转差率', '转速(rpm)', '转矩(N·m)', '电流(A)'],
                'data': [[f"{s:.3f}", f"{sp:.1f}", f"{t:.2f}", f"{i:.2f}"] 
                        for s, sp, t, i in zip(slip[::10], speed[::10], torque[::10], current[::10])]
            }
        }
        
    def simulate_sync_generator(self, circuit_config: Dict) -> Dict:
        """同步发电机仿真"""
        # 简化的同步发电机模型
        gen_params = self.get_component_parameters(circuit_config, 'sync_generator')
        
        # 发电机参数
        E0 = gen_params.get('emf', 400.0)  # 空载电动势
        Xs = gen_params.get('sync_reactance', 10.0)  # 同步电抗
        
        # 负载角范围
        delta = np.linspace(0, 90, 100)  # 功率角
        delta_rad = delta * np.pi / 180
        
        # 功率特性
        P = E0**2 * np.sin(delta_rad) / Xs  # 有功功率
        Q = E0**2 * (1 - np.cos(delta_rad)) / Xs  # 无功功率
        
        # 时间域仿真
        t_end = 1.0
        dt = 0.001
        time = np.arange(0, t_end, dt)
        f = 50.0  # 频率
        
        # 三相电压
        Va = E0 * np.sin(2*np.pi*f*time)
        Vb = E0 * np.sin(2*np.pi*f*time - 2*np.pi/3)
        Vc = E0 * np.sin(2*np.pi*f*time + 2*np.pi/3)
        
        return {
            'waveform': {
                'time': time,
                'voltage': Va,
                'current': Va / 100,  # 假设负载阻抗100欧
                'title': '同步发电机输出波形'
            },
            'characteristic': {
                'x_data': delta,
                'y_data': P,
                'x_label': '功率角 (度)',
                'y_label': '有功功率 (W)',
                'title': '同步发电机功率特性'
            },
            'phasor': {
                'voltage_phasors': [(E0, 0), (E0, -2*np.pi/3), (E0, 2*np.pi/3)],
                'current_phasors': [(E0/100, -np.pi/6)],
                'title': '同步发电机相量图'
            }
        }
        
    def simulate_transformer(self, circuit_config: Dict) -> Dict:
        """变压器仿真"""
        trans_params = self.get_component_parameters(circuit_config, 'transformer')
        
        # 变压器参数
        V1 = trans_params.get('primary_voltage', 220.0)
        V2 = trans_params.get('secondary_voltage', 24.0)
        n = trans_params.get('turns_ratio', V1/V2)
        
        # 负载变化
        load_resistance = np.linspace(1, 100, 50)
        
        # 计算特性
        I2 = V2 / load_resistance  # 二次电流
        I1 = I2 / n  # 一次电流
        efficiency = (V2 * I2) / (V1 * I1) * 100  # 效率
        
        # 时间域仿真
        t_end = 0.1
        dt = 0.0001
        time = np.arange(0, t_end, dt)
        f = 50.0
        
        # 一次侧电压
        v1 = V1 * np.sqrt(2) * np.sin(2*np.pi*f*time)
        # 二次侧电压
        v2 = V2 * np.sqrt(2) * np.sin(2*np.pi*f*time)
        
        return {
            'waveform': {
                'time': time,
                'voltage': v1,
                'current': v2/24,  # 假设24欧负载
                'title': '变压器电压电流波形'
            },
            'characteristic': {
                'x_data': load_resistance,
                'y_data': efficiency,
                'x_label': '负载电阻 (Ω)',
                'y_label': '效率 (%)',
                'title': '变压器效率特性'
            }
        }
        
    def simulate_basic_circuit(self, circuit_config: Dict) -> Dict:
        """基本电路仿真"""
        # 简单的RC电路仿真示例
        t_end = 1.0
        dt = 0.001
        time = np.arange(0, t_end, dt)
        
        # 假设参数
        V = 12.0
        R = 10.0
        C = 100e-6
        
        # RC充电过程
        voltage = V * (1 - np.exp(-time/(R*C)))
        current = (V/R) * np.exp(-time/(R*C))
        
        return {
            'waveform': {
                'time': time,
                'voltage': voltage,
                'current': current,
                'title': 'RC电路充电过程'
            }
        }
        
    def get_component_parameters(self, circuit_config: Dict, component_type: str) -> Dict:
        """获取指定类型元件的参数"""
        components = circuit_config.get('components', [])
        for comp in components:
            if comp['type'] == component_type:
                return comp.get('parameters', {})
        return {}
        
    def stop_simulation(self):
        """停止仿真"""
        self.is_running = False
