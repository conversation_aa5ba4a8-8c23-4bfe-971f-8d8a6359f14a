# 电机学仿真实验软件

一个基于PyQt5的电机学仿真实验软件，支持四种典型的电机学课程实验。

## 功能特点

### 🔧 核心功能
- **拖拽式电路设计**: 通过拖拽元件到画布上构建电路
- **实时连线**: 支持元件之间的可视化连线
- **参数调节**: 实时调整元件参数
- **仿真计算**: 基于数值方法的电路仿真
- **结果可视化**: 波形图、特性曲线、数据表格

### 🧪 支持的实验

#### 1. 直流电机特性实验
- 直流电机启动特性
- 机械特性曲线
- 电枢控制调速
- 转速-转矩关系

#### 2. 三相异步电机实验  
- Y-Δ启动特性
- 直接启动和转速特性
- 电流变化和转速连接
- 同步转速和转差率测量

#### 3. 同步发电机实验
- 并网运行特性
- 功率角特性
- 有功无功功率调节
- 相量图分析

#### 4. 变压器特性实验
- 空载和短路试验
- 负载特性
- 效率测量
- 电压调整率

### 📦 元件库

#### 电源类
- 直流电源 (DC)
- 交流电源 (AC) 
- 三相电源

#### 电机类
- 直流电机
- 三相异步电机
- 同步电机
- 同步发电机

#### 基本元件
- 电阻、电感、电容
- 变压器

#### 测量仪表
- 电流表、电压表
- 功率表、转速表
- 接触器/开关

## 安装说明

### 环境要求
- Python 3.7+
- PyQt5
- NumPy
- SciPy  
- Matplotlib

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd 电机学实验
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 运行程序
```bash
python main.py
```

## 使用说明

### 基本操作

1. **选择元件**: 在左侧元件库中点击所需元件
2. **放置元件**: 在中央画布上点击放置元件
3. **连接元件**: 拖拽元件端口进行连线
4. **设置参数**: 在右侧参数面板调整元件参数
5. **开始仿真**: 点击"开始仿真"按钮
6. **查看结果**: 在结果显示区域查看波形和数据

### 实验流程

1. **选择实验**: 从菜单栏选择预设实验模板
2. **搭建电路**: 根据实验要求搭建电路
3. **参数设置**: 调整元件参数
4. **运行仿真**: 执行仿真计算
5. **分析结果**: 查看波形图、特性曲线和数据表格
6. **保存实验**: 保存实验配置和结果

## 项目结构

```
电机学实验/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── gui/                   # GUI模块
│   ├── __init__.py
│   ├── experiment_canvas.py    # 实验画布
│   ├── component_library.py    # 元件库
│   ├── parameter_panel.py      # 参数面板
│   └── result_display.py       # 结果显示
├── experiments/           # 实验模块
│   ├── __init__.py
│   ├── experiment_manager.py   # 实验管理器
│   ├── simulation_engine.py    # 仿真引擎
│   └── experiment_templates.py # 实验模板
├── components/            # 元件模型
│   └── __init__.py
└── utils/                 # 工具模块
    └── __init__.py
```

## 技术架构

### GUI框架
- **PyQt5**: 主界面框架
- **QGraphicsView**: 画布绘制
- **Matplotlib**: 图表显示

### 仿真引擎
- **NumPy**: 数值计算
- **SciPy**: 科学计算
- **节点电压法**: 电路分析
- **数值积分**: 时域仿真

### 数据结构
- **元件模型**: 面向对象的元件封装
- **电路拓扑**: 节点-支路描述
- **参数管理**: 动态参数配置

## 开发说明

### 添加新元件
1. 在`components/`目录下创建元件类
2. 在`component_library.py`中添加元件按钮
3. 在`experiment_canvas.py`中添加绘制方法
4. 在`simulation_engine.py`中添加仿真逻辑

### 添加新实验
1. 在`experiment_templates.py`中定义实验模板
2. 在`experiment_manager.py`中添加仿真方法
3. 在主菜单中添加实验选项

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请联系开发团队。
