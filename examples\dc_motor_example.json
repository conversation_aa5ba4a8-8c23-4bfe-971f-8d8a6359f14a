{"experiment_name": "直流电机特性实验示例", "description": "演示直流电机启动和调速特性", "components": [{"type": "power_source", "name": "DC_Source_1", "position": [100, 200], "parameters": {"voltage": 220.0, "internal_resistance": 0.1}}, {"type": "dc_motor", "name": "DC_Motor_1", "position": [400, 200], "parameters": {"rated_voltage": 220.0, "rated_power": 1000.0, "resistance": 2.0, "inductance": 0.1, "back_emf_constant": 0.1}}, {"type": "resistor", "name": "Start_Resistor_1", "position": [250, 200], "parameters": {"resistance": 5.0}}, {"type": "ammeter", "name": "Ammeter_1", "position": [300, 200], "parameters": {"range": 10.0}}], "connections": [{"from_component": "DC_Source_1", "from_port": 0, "to_component": "Start_Resistor_1", "to_port": 0}, {"from_component": "Start_Resistor_1", "from_port": 1, "to_component": "Ammeter_1", "to_port": 0}, {"from_component": "Ammeter_1", "from_port": 1, "to_component": "DC_Motor_1", "to_port": 0}, {"from_component": "DC_Motor_1", "from_port": 1, "to_component": "DC_Source_1", "to_port": 1}], "simulation_parameters": {"simulation_time": 3.0, "time_step": 0.001, "solver": "euler"}}