#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电机学仿真实验软件
主程序入口
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PyQt5.QtWidgets import QMenuBar, QMenu, QAction, QToolBar, QStatusBar
from PyQt5.QtWidgets import QSplitter, QTabWidget
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

from gui.experiment_canvas import ExperimentCanvas
from gui.component_library import ComponentLibrary
from gui.parameter_panel import ParameterPanel
from gui.result_display import ResultDisplay
from experiments.experiment_manager import ExperimentManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("电机学仿真实验软件 v1.0")
        self.setGeometry(100, 100, 1400, 900)
        
        # 初始化实验管理器
        self.experiment_manager = ExperimentManager()
        
        # 设置中心部件
        self.setup_ui()
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 连接信号
        self.connect_signals()
        
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧：元件库
        self.component_library = ComponentLibrary()
        main_splitter.addWidget(self.component_library)
        
        # 中间：实验画布
        self.experiment_canvas = ExperimentCanvas()
        main_splitter.addWidget(self.experiment_canvas)
        
        # 右侧：参数面板和结果显示
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 参数面板
        self.parameter_panel = ParameterPanel()
        right_layout.addWidget(self.parameter_panel)
        
        # 结果显示
        self.result_display = ResultDisplay()
        right_layout.addWidget(self.result_display)
        
        main_splitter.addWidget(right_widget)
        
        # 设置分割器比例
        main_splitter.setSizes([250, 800, 350])
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        new_action = QAction('新建实验(&N)', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_experiment)
        file_menu.addAction(new_action)
        
        open_action = QAction('打开实验(&O)', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_experiment)
        file_menu.addAction(open_action)
        
        save_action = QAction('保存实验(&S)', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_experiment)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 实验菜单
        experiment_menu = menubar.addMenu('实验(&E)')
        
        dc_motor_action = QAction('直流电机特性实验', self)
        dc_motor_action.triggered.connect(lambda: self.load_experiment('dc_motor'))
        experiment_menu.addAction(dc_motor_action)
        
        induction_motor_action = QAction('三相异步电机实验', self)
        induction_motor_action.triggered.connect(lambda: self.load_experiment('induction_motor'))
        experiment_menu.addAction(induction_motor_action)
        
        sync_generator_action = QAction('同步发电机实验', self)
        sync_generator_action.triggered.connect(lambda: self.load_experiment('sync_generator'))
        experiment_menu.addAction(sync_generator_action)
        
        transformer_action = QAction('变压器特性实验', self)
        transformer_action.triggered.connect(lambda: self.load_experiment('transformer'))
        experiment_menu.addAction(transformer_action)
        
        # 仿真菜单
        simulation_menu = menubar.addMenu('仿真(&S)')
        
        start_sim_action = QAction('开始仿真(&S)', self)
        start_sim_action.setShortcut('F5')
        start_sim_action.triggered.connect(self.start_simulation)
        simulation_menu.addAction(start_sim_action)
        
        stop_sim_action = QAction('停止仿真(&T)', self)
        stop_sim_action.setShortcut('F6')
        stop_sim_action.triggered.connect(self.stop_simulation)
        simulation_menu.addAction(stop_sim_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 添加工具按钮
        new_action = QAction('新建', self)
        new_action.triggered.connect(self.new_experiment)
        toolbar.addAction(new_action)
        
        open_action = QAction('打开', self)
        open_action.triggered.connect(self.open_experiment)
        toolbar.addAction(open_action)
        
        save_action = QAction('保存', self)
        save_action.triggered.connect(self.save_experiment)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        start_action = QAction('开始仿真', self)
        start_action.triggered.connect(self.start_simulation)
        toolbar.addAction(start_action)
        
        stop_action = QAction('停止仿真', self)
        stop_action.triggered.connect(self.stop_simulation)
        toolbar.addAction(stop_action)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
    def connect_signals(self):
        """连接信号槽"""
        # 元件库信号
        self.component_library.component_selected.connect(
            self.experiment_canvas.set_current_component
        )
        
        # 画布信号
        self.experiment_canvas.component_added.connect(
            self.parameter_panel.update_component_list
        )
        
        self.experiment_canvas.component_selected.connect(
            self.parameter_panel.show_component_parameters
        )
        
        # 参数面板信号
        self.parameter_panel.parameter_changed.connect(
            self.experiment_canvas.update_component_parameter
        )
        
        self.parameter_panel.simulation_requested.connect(
            self.start_simulation
        )
        
    def new_experiment(self):
        """新建实验"""
        self.experiment_canvas.clear_canvas()
        self.parameter_panel.clear_parameters()
        self.result_display.clear_results()
        self.status_bar.showMessage("新建实验")
        
    def open_experiment(self):
        """打开实验"""
        # TODO: 实现文件打开对话框
        self.status_bar.showMessage("打开实验")
        
    def save_experiment(self):
        """保存实验"""
        # TODO: 实现文件保存对话框
        self.status_bar.showMessage("保存实验")
        
    def load_experiment(self, experiment_type):
        """加载预设实验"""
        self.experiment_manager.load_experiment_template(experiment_type)
        self.status_bar.showMessage(f"加载{experiment_type}实验模板")
        
    def start_simulation(self):
        """开始仿真"""
        try:
            # 获取电路配置
            circuit_config = self.experiment_canvas.get_circuit_configuration()
            
            # 运行仿真
            results = self.experiment_manager.run_simulation(circuit_config)
            
            # 显示结果
            self.result_display.show_results(results)
            
            self.status_bar.showMessage("仿真完成")
            
        except Exception as e:
            self.status_bar.showMessage(f"仿真错误: {str(e)}")
            
    def stop_simulation(self):
        """停止仿真"""
        self.experiment_manager.stop_simulation()
        self.status_bar.showMessage("仿真已停止")
        
    def show_about(self):
        """显示关于对话框"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.about(self, "关于", 
                         "电机学仿真实验软件 v1.0\n\n"
                         "支持四种典型电机学实验的仿真\n"
                         "包含拖拽式电路设计和实时仿真功能")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("电机学仿真实验软件")
    app.setApplicationVersion("1.0")
    
    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
