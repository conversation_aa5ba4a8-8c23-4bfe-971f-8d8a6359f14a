#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仿真引擎
提供电路分析和数值计算功能
"""

import numpy as np
import scipy.integrate as integrate
from typing import Dict, List, Tuple, Any


class CircuitNode:
    """电路节点"""
    
    def __init__(self, node_id: int):
        self.node_id = node_id
        self.voltage = 0.0
        self.connected_components = []


class CircuitComponent:
    """电路元件基类"""
    
    def __init__(self, comp_id: str, comp_type: str, nodes: List[int]):
        self.comp_id = comp_id
        self.comp_type = comp_type
        self.nodes = nodes
        self.parameters = {}
        
    def get_impedance(self, frequency: float = 0) -> complex:
        """获取阻抗"""
        return 1.0 + 0j
        
    def get_current(self, voltage: float) -> float:
        """获取电流"""
        return 0.0


class Resistor(CircuitComponent):
    """电阻元件"""
    
    def __init__(self, comp_id: str, nodes: List[int], resistance: float):
        super().__init__(comp_id, "resistor", nodes)
        self.resistance = resistance
        
    def get_impedance(self, frequency: float = 0) -> complex:
        return self.resistance + 0j
        
    def get_current(self, voltage: float) -> float:
        return voltage / self.resistance


class Inductor(CircuitComponent):
    """电感元件"""
    
    def __init__(self, comp_id: str, nodes: List[int], inductance: float):
        super().__init__(comp_id, "inductor", nodes)
        self.inductance = inductance
        
    def get_impedance(self, frequency: float = 0) -> complex:
        if frequency == 0:
            return 0 + 0j
        return 0 + 1j * 2 * np.pi * frequency * self.inductance


class Capacitor(CircuitComponent):
    """电容元件"""
    
    def __init__(self, comp_id: str, nodes: List[int], capacitance: float):
        super().__init__(comp_id, "capacitor", nodes)
        self.capacitance = capacitance
        
    def get_impedance(self, frequency: float = 0) -> complex:
        if frequency == 0 or self.capacitance == 0:
            return float('inf') + 0j
        return 0 - 1j / (2 * np.pi * frequency * self.capacitance)


class VoltageSource(CircuitComponent):
    """电压源"""
    
    def __init__(self, comp_id: str, nodes: List[int], voltage: float):
        super().__init__(comp_id, "voltage_source", nodes)
        self.voltage = voltage


class DCMotor(CircuitComponent):
    """直流电机模型"""
    
    def __init__(self, comp_id: str, nodes: List[int], parameters: Dict):
        super().__init__(comp_id, "dc_motor", nodes)
        self.resistance = parameters.get('resistance', 2.0)
        self.inductance = parameters.get('inductance', 0.1)
        self.back_emf_constant = parameters.get('back_emf_constant', 0.1)
        self.speed = 0.0  # 当前转速
        
    def get_back_emf(self) -> float:
        """获取反电动势"""
        return self.back_emf_constant * self.speed
        
    def update_speed(self, current: float, dt: float):
        """更新转速"""
        torque = self.back_emf_constant * current
        # 简化的转速更新 (忽略负载转矩)
        J = 0.01  # 转动惯量
        self.speed += (torque / J) * dt


class SimulationEngine:
    """仿真引擎"""
    
    def __init__(self):
        self.nodes = {}
        self.components = {}
        self.time_step = 0.001
        self.simulation_time = 1.0
        
    def add_node(self, node_id: int) -> CircuitNode:
        """添加节点"""
        if node_id not in self.nodes:
            self.nodes[node_id] = CircuitNode(node_id)
        return self.nodes[node_id]
        
    def add_component(self, component: CircuitComponent):
        """添加元件"""
        self.components[component.comp_id] = component
        
        # 确保节点存在
        for node_id in component.nodes:
            self.add_node(node_id)
            
    def solve_dc_circuit(self) -> Dict[str, float]:
        """求解直流电路"""
        # 使用节点电压法求解
        num_nodes = len(self.nodes)
        if num_nodes == 0:
            return {}
            
        # 构建导纳矩阵和电流向量
        G = np.zeros((num_nodes, num_nodes))  # 导纳矩阵
        I = np.zeros(num_nodes)  # 电流向量
        
        # 添加元件的导纳
        for comp in self.components.values():
            if comp.comp_type in ['resistor', 'inductor', 'capacitor']:
                impedance = comp.get_impedance(0)  # DC分析
                if impedance != 0:
                    conductance = 1.0 / impedance.real
                    
                    if len(comp.nodes) == 2:
                        n1, n2 = comp.nodes
                        G[n1, n1] += conductance
                        G[n2, n2] += conductance
                        G[n1, n2] -= conductance
                        G[n2, n1] -= conductance
                        
            elif comp.comp_type == 'voltage_source':
                # 电压源处理 (简化)
                if len(comp.nodes) == 2:
                    n1, n2 = comp.nodes
                    I[n1] += comp.voltage / 1e-6  # 添加大电流源模拟电压源
                    
        # 设置参考节点 (节点0为地)
        if 0 in self.nodes:
            G[0, :] = 0
            G[0, 0] = 1
            I[0] = 0
            
        # 求解节点电压
        try:
            node_voltages = np.linalg.solve(G, I)
            
            # 计算支路电流
            branch_currents = {}
            for comp_id, comp in self.components.items():
                if comp.comp_type == 'resistor' and len(comp.nodes) == 2:
                    n1, n2 = comp.nodes
                    voltage_diff = node_voltages[n1] - node_voltages[n2]
                    current = voltage_diff / comp.resistance
                    branch_currents[comp_id] = current
                    
            return {
                'node_voltages': {i: v for i, v in enumerate(node_voltages)},
                'branch_currents': branch_currents
            }
            
        except np.linalg.LinAlgError:
            return {'error': '电路矩阵奇异，无法求解'}
            
    def solve_ac_circuit(self, frequency: float) -> Dict[str, complex]:
        """求解交流电路"""
        num_nodes = len(self.nodes)
        if num_nodes == 0:
            return {}
            
        # 构建复数导纳矩阵
        Y = np.zeros((num_nodes, num_nodes), dtype=complex)
        I = np.zeros(num_nodes, dtype=complex)
        
        # 添加元件的导纳
        for comp in self.components.values():
            if comp.comp_type in ['resistor', 'inductor', 'capacitor']:
                impedance = comp.get_impedance(frequency)
                if impedance != 0:
                    admittance = 1.0 / impedance
                    
                    if len(comp.nodes) == 2:
                        n1, n2 = comp.nodes
                        Y[n1, n1] += admittance
                        Y[n2, n2] += admittance
                        Y[n1, n2] -= admittance
                        Y[n2, n1] -= admittance
                        
        # 设置参考节点
        if 0 in self.nodes:
            Y[0, :] = 0
            Y[0, 0] = 1
            I[0] = 0
            
        # 求解
        try:
            node_voltages = np.linalg.solve(Y, I)
            return {
                'node_voltages': {i: v for i, v in enumerate(node_voltages)},
                'frequency': frequency
            }
        except np.linalg.LinAlgError:
            return {'error': '电路矩阵奇异，无法求解'}
            
    def time_domain_simulation(self, circuit_config: Dict) -> Dict:
        """时域仿真"""
        # 解析电路配置
        self.parse_circuit_config(circuit_config)
        
        # 时间向量
        time = np.arange(0, self.simulation_time, self.time_step)
        
        # 初始化结果数组
        results = {
            'time': time,
            'voltages': {},
            'currents': {}
        }
        
        # 对于每个时间点进行仿真
        for t_idx, t in enumerate(time):
            # 更新时变源
            self.update_time_varying_sources(t)
            
            # 求解当前时刻的电路
            solution = self.solve_dc_circuit()
            
            # 存储结果
            if 'node_voltages' in solution:
                for node_id, voltage in solution['node_voltages'].items():
                    if node_id not in results['voltages']:
                        results['voltages'][node_id] = np.zeros_like(time)
                    results['voltages'][node_id][t_idx] = voltage
                    
            if 'branch_currents' in solution:
                for comp_id, current in solution['branch_currents'].items():
                    if comp_id not in results['currents']:
                        results['currents'][comp_id] = np.zeros_like(time)
                    results['currents'][comp_id][t_idx] = current
                    
        return results
        
    def parse_circuit_config(self, circuit_config: Dict):
        """解析电路配置"""
        self.nodes.clear()
        self.components.clear()
        
        # 添加元件
        for comp_config in circuit_config.get('components', []):
            comp_type = comp_config['type']
            comp_name = comp_config['name']
            parameters = comp_config.get('parameters', {})
            
            # 简化的节点分配 (实际应该根据连接信息确定)
            nodes = [0, 1]  # 默认连接到节点0和1
            
            if comp_type == 'resistor':
                resistance = parameters.get('resistance', 10.0)
                component = Resistor(comp_name, nodes, resistance)
            elif comp_type == 'inductor':
                inductance = parameters.get('inductance', 0.1)
                component = Inductor(comp_name, nodes, inductance)
            elif comp_type == 'capacitor':
                capacitance = parameters.get('capacitance', 100e-6)
                component = Capacitor(comp_name, nodes, capacitance)
            elif comp_type == 'power_source':
                voltage = parameters.get('voltage', 12.0)
                component = VoltageSource(comp_name, nodes, voltage)
            elif comp_type == 'dc_motor':
                component = DCMotor(comp_name, nodes, parameters)
            else:
                continue
                
            self.add_component(component)
            
    def update_time_varying_sources(self, time: float):
        """更新时变源"""
        for comp in self.components.values():
            if comp.comp_type == 'voltage_source':
                # 这里可以添加时变电压源的逻辑
                pass
                
    def frequency_response(self, start_freq: float, end_freq: float, 
                          num_points: int = 100) -> Dict:
        """频率响应分析"""
        frequencies = np.logspace(np.log10(start_freq), np.log10(end_freq), num_points)
        
        magnitude = []
        phase = []
        
        for freq in frequencies:
            solution = self.solve_ac_circuit(freq)
            if 'node_voltages' in solution:
                # 计算传递函数 (简化)
                if len(solution['node_voltages']) > 1:
                    H = solution['node_voltages'][1] / solution['node_voltages'][0]
                    magnitude.append(abs(H))
                    phase.append(np.angle(H) * 180 / np.pi)
                else:
                    magnitude.append(0)
                    phase.append(0)
            else:
                magnitude.append(0)
                phase.append(0)
                
        return {
            'frequencies': frequencies,
            'magnitude': magnitude,
            'phase': phase
        }
