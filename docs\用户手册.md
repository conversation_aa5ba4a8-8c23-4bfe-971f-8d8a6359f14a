# 电机学仿真实验软件用户手册

## 软件概述

本软件是一个专门为电机学课程设计的仿真实验平台，支持四种典型的电机学实验：
1. 直流电机特性实验
2. 三相异步电机实验  
3. 同步发电机实验
4. 变压器特性实验

## 界面介绍

### 主界面布局
- **左侧**: 元件库面板 - 包含各种电路元件
- **中央**: 实验画布 - 用于搭建电路和连线
- **右侧**: 参数设置和结果显示面板

### 菜单栏功能
- **文件菜单**: 新建、打开、保存实验
- **实验菜单**: 选择预设实验模板
- **仿真菜单**: 开始/停止仿真
- **帮助菜单**: 软件信息

## 基本操作

### 1. 添加元件
1. 在左侧元件库中点击所需元件
2. 在中央画布上点击放置元件
3. 元件会出现在点击位置

### 2. 移动元件
- 直接拖拽元件到新位置

### 3. 连接元件
1. 点击起始元件的输出端口（绿色圆点）
2. 拖拽到目标元件的输入端口
3. 释放鼠标完成连接

### 4. 设置参数
1. 点击选择元件
2. 在右侧参数面板中调整参数值
3. 参数会实时更新

### 5. 运行仿真
1. 搭建完整电路
2. 设置仿真参数（时间、步长等）
3. 点击"开始仿真"按钮
4. 在结果面板查看仿真结果

## 实验指导

### 直流电机特性实验

**实验目的**:
- 了解直流电机的机械特性
- 掌握直流电机的启动过程
- 学习电枢电阻对转速的影响

**实验步骤**:
1. 选择"实验" → "直流电机特性实验"
2. 观察预设的电路连接
3. 调整电源电压和电机参数
4. 运行仿真，观察启动过程
5. 查看转速-转矩特性曲线

**关键参数**:
- 电源电压: 220V
- 电机电阻: 2Ω
- 电机电感: 0.1H
- 反电动势常数: 0.1

### 三相异步电机实验

**实验目的**:
- 掌握异步电机Y-Δ启动
- 了解直接启动和转速特性
- 观察电流变化和转速连接

**实验步骤**:
1. 选择"实验" → "三相异步电机实验"
2. 设置三相电源参数
3. 调整电机极数和转差率
4. 运行仿真，观察启动电流
5. 分析机械特性曲线

**关键参数**:
- 线电压: 380V
- 频率: 50Hz
- 极数: 4
- 额定转差率: 0.05

### 同步发电机实验

**实验目的**:
- 观察同步发电机并网时的电压相位
- 掌握功率因数可调功率因数
- 了解并网条件

**实验步骤**:
1. 选择"实验" → "同步发电机实验"
2. 设置发电机参数
3. 调整励磁电流
4. 观察相量图变化
5. 分析功率特性

### 变压器特性实验

**实验目的**:
- 掌握空载试验、短路试验
- 了解负载试验、负载特性
- 测量变压器效率化规律

**实验步骤**:
1. 选择"实验" → "变压器特性实验"
2. 设置变压器变比
3. 改变负载阻值
4. 观察电压调整率
5. 分析效率特性

## 元件说明

### 电源类
- **直流电源**: 提供稳定直流电压
- **交流电源**: 提供正弦交流电压
- **三相电源**: 提供三相对称电源

### 电机类
- **直流电机**: 支持启动和调速仿真
- **异步电机**: 三相异步电机模型
- **同步电机**: 同步电机/发电机模型

### 基本元件
- **电阻**: 线性电阻元件
- **电感**: 线性电感元件
- **电容**: 线性电容元件
- **变压器**: 理想变压器模型

### 测量仪表
- **电流表**: 测量支路电流
- **电压表**: 测量节点电压
- **功率表**: 测量功率
- **转速表**: 测量电机转速

## 仿真结果

### 波形图
显示电压、电流随时间的变化波形

### 特性曲线
显示各种特性关系，如:
- 转速-转矩特性
- 效率-负载特性
- 功率-功率角特性

### 数据表格
以表格形式显示详细的数值数据

### 相量图
显示交流电路的相量关系

## 常见问题

### Q: 元件无法连接？
A: 确保连接的是正确的端口（输出端口连接到输入端口）

### Q: 仿真结果异常？
A: 检查电路连接是否正确，参数设置是否合理

### Q: 程序运行缓慢？
A: 减小仿真时间或增大时间步长

### Q: 无法保存实验？
A: 确保有足够的磁盘空间和写入权限

## 技术支持

如遇到技术问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整
3. 系统兼容性

更多帮助请参考README.md文件或联系开发团队。
