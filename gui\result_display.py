#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果显示模块
显示仿真结果、波形图和数据表格
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib
matplotlib.use('Qt5Agg')

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QTableWidget, QTableWidgetItem, QLabel, QPushButton,
                            QGroupBox, QTextEdit, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class PlotCanvas(FigureCanvas):
    """绘图画布"""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
    def plot_waveform(self, time_data, voltage_data, current_data, title="仿真波形"):
        """绘制波形图"""
        self.fig.clear()
        
        # 创建子图
        ax1 = self.fig.add_subplot(211)
        ax2 = self.fig.add_subplot(212)
        
        # 绘制电压波形
        ax1.plot(time_data, voltage_data, 'b-', linewidth=2, label='电压')
        ax1.set_ylabel('电压 (V)')
        ax1.set_title(title)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 绘制电流波形
        ax2.plot(time_data, current_data, 'r-', linewidth=2, label='电流')
        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('电流 (A)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 调整布局
        self.fig.tight_layout()
        self.draw()
        
    def plot_characteristic_curve(self, x_data, y_data, x_label, y_label, title):
        """绘制特性曲线"""
        self.fig.clear()
        
        ax = self.fig.add_subplot(111)
        ax.plot(x_data, y_data, 'bo-', linewidth=2, markersize=6)
        ax.set_xlabel(x_label)
        ax.set_ylabel(y_label)
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        
        self.fig.tight_layout()
        self.draw()
        
    def plot_phasor_diagram(self, voltage_phasors, current_phasors, title="相量图"):
        """绘制相量图"""
        self.fig.clear()
        
        ax = self.fig.add_subplot(111, projection='polar')
        
        # 绘制电压相量
        for i, (mag, phase) in enumerate(voltage_phasors):
            ax.arrow(0, 0, phase, mag, head_width=0.1, head_length=0.1, 
                    fc=f'C{i}', ec=f'C{i}', label=f'U{i+1}')
                    
        # 绘制电流相量
        for i, (mag, phase) in enumerate(current_phasors):
            ax.arrow(0, 0, phase, mag, head_width=0.1, head_length=0.1,
                    fc=f'C{i+3}', ec=f'C{i+3}', label=f'I{i+1}', linestyle='--')
                    
        ax.set_title(title)
        ax.legend()
        
        self.draw()


class DataTable(QTableWidget):
    """数据表格"""
    
    def __init__(self):
        super().__init__()
        self.setup_table()
        
    def setup_table(self):
        """设置表格"""
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 5px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
        
    def update_data(self, headers, data):
        """更新表格数据"""
        self.setRowCount(len(data))
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                self.setItem(row, col, item)
                
        # 调整列宽
        self.resizeColumnsToContents()


class ResultDisplay(QWidget):
    """结果显示面板"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("仿真结果")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 波形图选项卡
        self.waveform_tab = QWidget()
        self.setup_waveform_tab()
        self.tab_widget.addTab(self.waveform_tab, "波形图")
        
        # 特性曲线选项卡
        self.curve_tab = QWidget()
        self.setup_curve_tab()
        self.tab_widget.addTab(self.curve_tab, "特性曲线")
        
        # 数据表格选项卡
        self.data_tab = QWidget()
        self.setup_data_tab()
        self.tab_widget.addTab(self.data_tab, "数据表格")
        
        # 相量图选项卡
        self.phasor_tab = QWidget()
        self.setup_phasor_tab()
        self.tab_widget.addTab(self.phasor_tab, "相量图")
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.export_button = QPushButton("导出数据")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(self.export_button)
        
        self.clear_button = QPushButton("清空结果")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.clear_button.clicked.connect(self.clear_results)
        button_layout.addWidget(self.clear_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
    def setup_waveform_tab(self):
        """设置波形图选项卡"""
        layout = QVBoxLayout(self.waveform_tab)
        
        self.waveform_canvas = PlotCanvas(self.waveform_tab, width=6, height=4)
        layout.addWidget(self.waveform_canvas)
        
    def setup_curve_tab(self):
        """设置特性曲线选项卡"""
        layout = QVBoxLayout(self.curve_tab)
        
        self.curve_canvas = PlotCanvas(self.curve_tab, width=6, height=4)
        layout.addWidget(self.curve_canvas)
        
    def setup_data_tab(self):
        """设置数据表格选项卡"""
        layout = QVBoxLayout(self.data_tab)
        
        self.data_table = DataTable()
        layout.addWidget(self.data_table)
        
    def setup_phasor_tab(self):
        """设置相量图选项卡"""
        layout = QVBoxLayout(self.phasor_tab)
        
        self.phasor_canvas = PlotCanvas(self.phasor_tab, width=6, height=4)
        layout.addWidget(self.phasor_canvas)
        
    def show_results(self, results):
        """显示仿真结果"""
        if not results:
            return
            
        # 显示波形图
        if 'waveform' in results:
            waveform_data = results['waveform']
            self.waveform_canvas.plot_waveform(
                waveform_data.get('time', []),
                waveform_data.get('voltage', []),
                waveform_data.get('current', []),
                waveform_data.get('title', '仿真波形')
            )
            
        # 显示特性曲线
        if 'characteristic' in results:
            char_data = results['characteristic']
            self.curve_canvas.plot_characteristic_curve(
                char_data.get('x_data', []),
                char_data.get('y_data', []),
                char_data.get('x_label', 'X'),
                char_data.get('y_label', 'Y'),
                char_data.get('title', '特性曲线')
            )
            
        # 显示数据表格
        if 'table_data' in results:
            table_data = results['table_data']
            self.data_table.update_data(
                table_data.get('headers', []),
                table_data.get('data', [])
            )
            
        # 显示相量图
        if 'phasor' in results:
            phasor_data = results['phasor']
            self.phasor_canvas.plot_phasor_diagram(
                phasor_data.get('voltage_phasors', []),
                phasor_data.get('current_phasors', []),
                phasor_data.get('title', '相量图')
            )
            
    def clear_results(self):
        """清空结果"""
        # 清空所有画布
        self.waveform_canvas.fig.clear()
        self.waveform_canvas.draw()
        
        self.curve_canvas.fig.clear()
        self.curve_canvas.draw()
        
        self.phasor_canvas.fig.clear()
        self.phasor_canvas.draw()
        
        # 清空数据表格
        self.data_table.setRowCount(0)
        self.data_table.setColumnCount(0)
        
    def export_data(self):
        """导出数据"""
        # TODO: 实现数据导出功能
        pass
