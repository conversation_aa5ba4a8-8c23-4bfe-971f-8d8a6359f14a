#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元件库模块
提供可拖拽的电路元件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QScrollArea, QFrame, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont


class ComponentButton(QPushButton):
    """元件按钮"""
    
    def __init__(self, component_type, display_name, description=""):
        super().__init__()
        self.component_type = component_type
        self.display_name = display_name
        self.description = description
        
        # 设置按钮样式
        self.setFixedSize(80, 60)
        self.setText(display_name)
        self.setToolTip(description)
        
        # 设置样式表
        self.setStyleSheet("""
            QPushButton {
                border: 2px solid #8f8f91;
                border-radius: 6px;
                background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #f6f7fa, stop: 1 #dadbde);
                min-width: 80px;
                font-size: 10px;
            }
            QPushButton:pressed {
                background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #dadbde, stop: 1 #f6f7fa);
            }
            QPushButton:hover {
                border: 2px solid #0078d4;
            }
        """)


class ComponentLibrary(QWidget):
    """元件库面板"""
    
    component_selected = pyqtSignal(str)  # 元件选择信号
    
    def __init__(self):
        super().__init__()
        self.setFixedWidth(250)
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("元件库")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        layout.addWidget(scroll_area)
        
        # 滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 电源组
        power_group = self.create_component_group("电源", [
            ("power_source", "直流电源", "提供直流电压"),
            ("ac_source", "交流电源", "提供交流电压"),
            ("three_phase_source", "三相电源", "提供三相交流电源")
        ])
        scroll_layout.addWidget(power_group)
        
        # 电机组
        motor_group = self.create_component_group("电机", [
            ("dc_motor", "直流电机", "直流电机"),
            ("induction_motor", "异步电机", "三相异步电机"),
            ("sync_motor", "同步电机", "同步电机"),
            ("sync_generator", "同步发电机", "同步发电机")
        ])
        scroll_layout.addWidget(motor_group)
        
        # 基本元件组
        basic_group = self.create_component_group("基本元件", [
            ("resistor", "电阻", "线性电阻"),
            ("inductor", "电感", "线性电感"),
            ("capacitor", "电容", "线性电容"),
            ("transformer", "变压器", "理想变压器")
        ])
        scroll_layout.addWidget(basic_group)
        
        # 测量仪表组
        meter_group = self.create_component_group("测量仪表", [
            ("ammeter", "电流表", "测量电流"),
            ("voltmeter", "电压表", "测量电压"),
            ("wattmeter", "功率表", "测量功率"),
            ("tachometer", "转速表", "测量转速")
        ])
        scroll_layout.addWidget(meter_group)
        
        # 控制元件组
        control_group = self.create_component_group("控制元件", [
            ("switch", "开关", "控制开关"),
            ("contactor", "接触器", "电磁接触器"),
            ("relay", "继电器", "控制继电器")
        ])
        scroll_layout.addWidget(control_group)
        
        # 设置滚动内容
        scroll_area.setWidget(scroll_content)
        
        # 添加弹性空间
        scroll_layout.addStretch()
        
    def create_component_group(self, group_name, components):
        """创建元件组"""
        group_box = QGroupBox(group_name)
        group_box.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        
        # 设置组框样式
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group_box)
        
        # 创建按钮网格
        row_layout = None
        for i, (comp_type, display_name, description) in enumerate(components):
            if i % 2 == 0:  # 每行两个按钮
                row_layout = QHBoxLayout()
                layout.addLayout(row_layout)
                
            button = ComponentButton(comp_type, display_name, description)
            button.clicked.connect(lambda checked, ct=comp_type: self.on_component_selected(ct))
            row_layout.addWidget(button)
            
        # 如果最后一行只有一个按钮，添加弹性空间
        if len(components) % 2 == 1:
            row_layout.addStretch()
            
        return group_box
        
    def on_component_selected(self, component_type):
        """元件选择事件"""
        self.component_selected.emit(component_type)
        
        # 更新状态显示
        self.update_selection_status(component_type)
        
    def update_selection_status(self, component_type):
        """更新选择状态"""
        # 重置所有按钮状态
        for button in self.findChildren(ComponentButton):
            if button.component_type == component_type:
                button.setStyleSheet(button.styleSheet() + """
                    QPushButton {
                        background-color: #0078d4;
                        color: white;
                    }
                """)
            else:
                # 恢复默认样式
                button.setStyleSheet("""
                    QPushButton {
                        border: 2px solid #8f8f91;
                        border-radius: 6px;
                        background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                        stop: 0 #f6f7fa, stop: 1 #dadbde);
                        min-width: 80px;
                        font-size: 10px;
                    }
                    QPushButton:pressed {
                        background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                        stop: 0 #dadbde, stop: 1 #f6f7fa);
                    }
                    QPushButton:hover {
                        border: 2px solid #0078d4;
                    }
                """)
                
    def get_component_info(self, component_type):
        """获取元件信息"""
        component_info = {
            # 电源
            "power_source": {
                "name": "直流电源",
                "parameters": {"voltage": 12.0, "internal_resistance": 0.1},
                "description": "提供稳定的直流电压"
            },
            "ac_source": {
                "name": "交流电源", 
                "parameters": {"voltage": 220.0, "frequency": 50.0, "phase": 0.0},
                "description": "提供正弦交流电压"
            },
            "three_phase_source": {
                "name": "三相电源",
                "parameters": {"voltage": 380.0, "frequency": 50.0},
                "description": "提供三相对称交流电源"
            },
            
            # 电机
            "dc_motor": {
                "name": "直流电机",
                "parameters": {"rated_voltage": 220.0, "rated_power": 1000.0, 
                             "resistance": 2.0, "inductance": 0.1},
                "description": "直流电机模型"
            },
            "induction_motor": {
                "name": "异步电机",
                "parameters": {"rated_voltage": 380.0, "rated_power": 1500.0,
                             "poles": 4, "slip": 0.05},
                "description": "三相异步电机模型"
            },
            
            # 基本元件
            "resistor": {
                "name": "电阻",
                "parameters": {"resistance": 10.0},
                "description": "线性电阻元件"
            },
            "inductor": {
                "name": "电感",
                "parameters": {"inductance": 0.1},
                "description": "线性电感元件"
            },
            "capacitor": {
                "name": "电容",
                "parameters": {"capacitance": 100e-6},
                "description": "线性电容元件"
            },
            
            # 测量仪表
            "ammeter": {
                "name": "电流表",
                "parameters": {"range": 10.0},
                "description": "电流测量仪表"
            },
            "voltmeter": {
                "name": "电压表", 
                "parameters": {"range": 500.0},
                "description": "电压测量仪表"
            }
        }
        
        return component_info.get(component_type, {
            "name": component_type,
            "parameters": {},
            "description": "未知元件"
        })
